This folder contains the source files for http://simplehtmldom.sourceforge.net/,
the project page for PHP Simple HTML DOM Parser.

Source files are written in Markdown: https://en.wikipedia.org/wiki/Markdown

Site data is generated by MkDocs, a lightweight static site generator for project
documentation: https://www.mkdocs.org/

# Folder structure

`custom_theme` : Contains customizations to the theme provided by MkDocs.
`docs` : Contains the source files for the project page (the actual pages).
`site` : Contains the output files for the project page when build with MkDocs.
`extra.css` : Customizations to the styles provided by MkDocs.
`mkdocs.yml` : The configuration file that is used by MkDocs to generate pages.

# Adding new pages

Place new files in `source`. Use subfolders (as few levels as possible) to
separate categories.

Files added to the manual will **not** appear on the project page automatically.
All pages need to be specified in the _mkdocs.yml_ file under "nav:". Simply add
the relative path to the new file where appropriate.

Note: Files are not added automatically because they are sorted by name if not
specified manually. Since readability is key factor for manuals, the files must
be sorted in a way that makes it clear to users.

# Setting up MkDocs

The installation instructions for MkDocs are provided on their homepage:
https://www.mkdocs.org/#installation

MkDocs automatically builds the project based on the _mkdocs.yml_ file. Find the
specification for this file at https://www.mkdocs.org/user-guide/configuration/.

# Building project pages

The build process depends on your installation of MkDocs. Typically MkDocs is
made available via the command line.

## Step 1 - Check your version of MkDocs

To check your version of MkDocs run this command:

`mkdocs --version` or
`python3 -m mkdocs --version`

Should return `version 1.0.4` or higher. If it doesn't make sure to install the
latest version using `pip install mkdocs` or `python3 -m pip install mkdocs`. If
you don't have pip installed, install it via package manager or follow the
instructions at https://pip.pypa.io/en/stable/installing/

## Step 2 - View the project locally

MkDocs allows you to view the project files in a browser on your local machine:

`mkdocs serve` or
`python3 -m mkdocs serve`

If the process is successful you can access the site at http://127.0.0.1:8000.

## Step 3 - Build the project

If you are satisfied with the results of the project, build the final project
with this command:

`mkdocs build` or
`python3 -m mkdocs build`

Find the output files in the `site` folder.