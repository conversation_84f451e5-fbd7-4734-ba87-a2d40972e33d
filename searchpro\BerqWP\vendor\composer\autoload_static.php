<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit5473ad16d4739935d061475992973f46
{
    public static $prefixLengthsPsr4 = array (
        'B' => 
        array (
            'BerqWP\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'BerqWP\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit5473ad16d4739935d061475992973f46::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit5473ad16d4739935d061475992973f46::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit5473ad16d4739935d061475992973f46::$classMap;

        }, null, ClassLoader::class);
    }
}
