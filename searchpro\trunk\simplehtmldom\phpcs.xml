<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Ruleset">
  <description>Created with the PHP Coding Standard Generator. http://edorian.github.com/php-coding-standard-generator/</description>
  <exclude-pattern>./app</exclude-pattern>
  <exclude-pattern>./example</exclude-pattern>
  <exclude-pattern>./manual</exclude-pattern>
  <exclude-pattern>./testcase</exclude-pattern>
  <rule ref="Generic.WhiteSpace.DisallowSpaceIndent"/>
  <rule ref="Generic.Classes.DuplicateClassName"/>
  <rule ref="PSR2.Methods.MethodDeclaration" />
  <rule ref="Generic.CodeAnalysis.EmptyStatement"/>
  <rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
  <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
  <rule ref="Generic.CodeAnalysis.UselessOverridingMethod"/>
  <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>
  <rule ref="Generic.Functions.OpeningFunctionBraceBsdAllman"/>
  <rule ref="PEAR.Functions.ValidDefaultValue"/>
  <rule ref="PSR2.ControlStructures.ElseIfDeclaration"/>
  <rule ref="PSR2.ControlStructures.ControlStructureSpacing"/>
  <rule ref="Squiz.WhiteSpace.CastSpacing"/>
  <rule ref="Squiz.WhiteSpace.OperatorSpacing"/>
  <rule ref="Squiz.WhiteSpace.SemicolonSpacing"/>
  <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace"/>
  <rule ref="Squiz.Strings.ConcatenationSpacing">
    <properties>
      <property name="spacing" value="1"/>
      <property name="ignoreNewlines" value="true"/>
    </properties>
  </rule>
  <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing">
    <properties>
      <property name="equalsSpacing" value="1"/>
    </properties>
  </rule>
  <rule ref="Generic.Files.LineLength">
    <properties>
      <property name="lineLimit" value="80"/>
      <property name="absoluteLineLimit" value="120"/>
    </properties>
  </rule>
  <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>
  <rule ref="Generic.PHP.LowerCaseConstant"/>
  <rule ref="Squiz.Strings.DoubleQuoteUsage">
    <exclude name="Squiz.Strings.DoubleQuoteUsage.ContainsVar" />
  </rule>
  <rule ref="Generic.Strings.UnnecessaryStringConcat"/>
  <rule ref="PSR2.Files.EndFileNewline"/>
</ruleset>
