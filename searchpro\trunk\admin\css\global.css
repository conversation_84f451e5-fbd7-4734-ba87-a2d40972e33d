.bwp-notice {
  display: flex;
  margin: 10px 0 !important;
  gap: 20px;
  padding: 25px 20px !important;
  border-radius: 10px;
  position: relative;
  box-shadow: none !important;
  background-color: #ffffff !important;
  border: 1px solid #ddeaff !important;
  box-shadow: 0px 0px 5px 2px rgba(83, 113, 167, 0.05) !important;
  border-radius: 0px;
  background-color: #e6e8ff !important;
  border: 1px solid #ded5ff !important;
  box-shadow: 0px 10px 50px -10px rgba(131, 125, 207, 0.2901960784) !important;
  margin: 20px auto !important;
  margin-right: 20px !important;
}
.bwp-notice.no-title {
  align-items: center;
}
.bwp-notice.status-info {
  background-color: #e6e8ff !important;
  border: 1px solid #ded5ff !important;
}
.bwp-notice.status-error {
  background-color: #ffe1e1 !important;
  border: 1px solid #ffbaba !important;
}
.bwp-notice.status-warning {
  background-color: #ffe6c7 !important;
  border: 1px solid #ffd094 !important;
}
.bwp-notice.status-success {
  background-color: #adffc0 !important;
  border: 1px solid #8ff783 !important;
}
.bwp-notice .icon {
  min-width: 50px;
  width: 50px;
  max-width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bwp-notice .content p {
  color: #061a3c;
}
.bwp-notice .status-tag {
  text-transform: capitalize;
  background-color: #a4a4a4;
  display: inline-block;
  color: #fff;
  padding: 3px 10px;
  font-weight: 400;
  font-size: 12px;
  border-radius: 13px;
  margin-bottom: 5px;
  display: none;
}
.bwp-notice .close {
  position: absolute;
  right: 20px;
  top: 20px;
  opacity: 1;
  cursor: pointer;
}
.bwp-notice.error .status-tag {
  background-color: #ff4c4c !important;
}
.bwp-notice.warning .status-tag {
  background-color: #ffb000 !important;
}
.bwp-notice.success .status-tag {
  background-color: #1dde59 !important;
}
.bwp-notice .content h5 {
  font-size: 15px;
  margin: 0;
  margin-bottom: 10px;
  color: #061a3c;
}
.bwp-notice .content p {
  font-size: 14px;
  padding: 0;
  color: #061a3c;
}
.bwp-notice .content .bwp-notice-btn {
  display: flex;
  margin-top: 14px;
  gap: 8px;
}
.bwp-notice .content .bwp-notice-btn .bwp-btn {
  text-decoration: none;
  color: #061a3c;
  background: #fff;
  padding: 10px 15px;
  display: block;
  line-height: 1;
  font-size: 13px;
  border-radius: 6px;
  border: 1px solid #dbe9fe;
}
.bwp-notice .content .bwp-notice-btn .bwp-btn.primary {
  background-color: #5057ff !important;
  color: #fff !important;
}

.berqwp-loader {
  width: 20px;
  float: left;
  margin-right: 10px;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 4px solid #e5e6ff;
  border-right-color: #5057ff;
  animation: l2 1s infinite linear;
}

@keyframes l2 {
  to {
    transform: rotate(1turn);
  }
}
.bwp-notice.bwp_feedback {
  display: none;
}/*# sourceMappingURL=global.css.map */