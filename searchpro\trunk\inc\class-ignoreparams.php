<?php

// Special Thanks: https://github.com/mpchadwick/tracking-query-params-registry/blob/master/_data/params.csv

class ignoreParams {
    static $query_params = [
        'Browser',
        'C',
        'GCCON',
        'MCMP',
        'MarketPlace',
        'PD',
        'Refresh',
        'Sens',
        'ServiceVersion',
        'Source',
        'Topic',
        '__WB_REVISION__',
        '__cf_chl_jschl_tk__',
        '__d',
        '__hsfp',
        '__hssc',
        '__hstc',
        '__s',
        '_branch_match_id',
        '_bta_c',
        '_bta_tid',
        '_com',
        '_escaped_fragment_',
        '_ga',
        '_ga-ft',
        '_gl',
        '_hsmi',
        '_ke',
        '_kx',
        '_paged',
        '_sm_byp',
        '_sp',
        '_szp',
        '_thumbnail_id',
        '3x',
        'a',
        'a_k',
        'ac',
        'acpage',
        'action-box',
        'action_object_map',
        'action_ref_map',
        'action_type_map',
        'activecampaign_id',
        'ad',
        'ad_frame_full',
        'ad_frame_root',
        'ad_name',
        'adclida',
        'adid',
        'adlt',
        'adsafe_ip',
        'adset_name',
        'advid',
        'aff_sub2',
        'afftrack',
        'afterload',
        'ak_action',
        'alt_id',
        'am',
        'amazingmurphybeds',
        'amp;',
        'amp;amp',
        'amp;amp;amp',
        'amp;amp;amp;amp',
        'amp;utm_campaign',
        'amp;utm_medium',
        'amp;utm_source',
        'amp%3Butm_content',
        'ampStoryAutoAnalyticsLinker',
        'ampstoryautoanalyticslinke',
        'an',
        'ap',
        'ap_id',
        'apif',
        'apipage',
        'as_occt',
        'as_q',
        'as_qdr',
        'askid',
        'atFileReset',
        'atfilereset',
        'aucid',
        'auct',
        'audience',
        'author',
        'awt_a',
        'awt_l',
        'awt_m',
        'b2w',
        'back',
        'bannerID',
        'blackhole',
        'blockedAdTracking',
        'blog-reader-used',
        'blogger',
        'body',
        'br',
        'bsft_aaid',
        'bsft_clkid',
        'bsft_eid',
        'bsft_ek',
        'bsft_lx',
        'bsft_mid',
        'bsft_mime_type',
        'bsft_tv',
        'bsft_uid',
        'bvMethod',
        'bvTime',
        'bvVersion',
        'bvb64',
        'bvb64resp',
        'bvplugname',
        'bvprms',
        'bvprmsmac',
        'bvreqmerge',
        'cacheburst',
        'campaign',
        'campaign_id',
        'campaign_name',
        'campid',
        'catablog-gallery',
        'channel',
        'checksum',
        'ck_subscriber_id',
        'cmplz_region_redirect',
        'cmpnid',
        'cn-reloaded',
        'code',
        'comment',
        'content_ad_widget',
        'cost',
        'cr',
        'crl8_id',
        'crlt.pid',
        'crlt_pid',
        'crrelr',
        'crtvid',
        'ct',
        'cuid',
        'daksldlkdsadas',
        'dcc',
        'dfp',
        'dm_i',
        'domain',
        'dosubmit',
        'dsp_caid',
        'dsp_crid',
        'dsp_insertion_order_id',
        'dsp_pub_id',
        'dsp_tracker_token',
        'dt',
        'dur',
        'durs',
        'e',
        'ee',
        'ef_id',
        'el',
        'emailID',
        'env',
        'epik',
        'erprint',
        'et_blog',
        'exch',
        'externalid',
        'fb_action_ids',
        'fb_action_types',
        'fb_ad',
        'fb_source',
        'fbclid',
        'fbzunique',
        'fg-aqp',
        'fireglass_rsn',
        'firstName',
        'fo',
        'fp_sid',
        'fpa',
        'fref',
        'fs',
        'furl',
        'fwp_lunch_restrictions',
        'ga_action',
        'gclid',
        'gclsrc',
        'gdffi',
        'gdfms',
        'gdftrk',
        'gf_page',
        'gidzl',
        'goal',
        'gooal',
        'gpu',
        'gtVersion',
        'haibwc',
        'hash',
        'hc_location',
        'hemail',
        'hid',
        'highlight',
        'hl',
        'home',
        'hsa_acc',
        'hsa_ad',
        'hsa_cam',
        'hsa_grp',
        'hsa_kw',
        'hsa_mt',
        'hsa_net',
        'hsa_src',
        'hsa_tgt',
        'hsa_ver',
        'ias_campId',
        'ias_chanId',
        'ias_dealId',
        'ias_dspId',
        'ias_impId',
        'ias_placementId',
        'ias_pubId',
        'ical',
        'ict',
        'ie',
        'igshid',
        'im',
        'ipl',
        'jw_start',
        'jwsource',
        'k',
        'key1',
        'key2',
        'klaviyo',
        'ksconf',
        'ksref',
        'l',
        'label',
        'lang',
        'ldtag_cl',
        'level1',
        'level2',
        'level3',
        'level4',
        'limit',
        'lng',
        'load_all_comments',
        'lt',
        'ltclid',
        'ltd',
        'lucky',
        'm',
        'm?sales_kw',
        'matomo_campaign',
        'matomo_cid',
        'matomo_content',
        'matomo_group',
        'matomo_keyword',
        'matomo_medium',
        'matomo_placement',
        'matomo_source',
        'max-results',
        'mc_cid',
        'mc_eid',
        'mdrv',
        'mediaserver',
        'memset',
        'mibextid',
        'mkcid',
        'mkevt',
        'mkrid',
        'mkwid',
        'mkt_tok',
        'ml_subscriber',
        'ml_subscriber_hash',
        'mobileOn',
        'mode',
        'moderation-hash',
        'modernpatio',
        'month',
        'msID',
        'msclkid',
        'msg',
        'mtm_campaign',
        'mtm_cid',
        'mtm_content',
        'mtm_group',
        'mtm_keyword',
        'mtm_medium',
        'mtm_placement',
        'mtm_source',
        'murphybedstoday',
        'mwprid',
        'n',
        'name',
        'native_client',
        'navua',
        'nb',
        'nb_klid',
        'nowprocketcache',
        'o',
        'okijoouuqnqq',
        'org',
        'pa_service_worker',
        'partnumber',
        'pcmtid',
        'pcode',
        'pcrid',
        'pfstyle',
        'phrase',
        'pid',
        'piwik_campaign',
        'piwik_keyword',
        'piwik_kwd',
        'pk_campaign',
        'pk_keyword',
        'pk_kwd',
        'placement',
        'plat',
        'platform',
        'playsinline',
        'position',
        'pp',
        'pr',
        'preview',
        'preview_id',
        'preview_nonce',
        'prid',
        'print',
        'q',
        'q1',
        'qsrc',
        'r',
        'rd',
        'rdt_cid',
        'redig',
        'redir',
        'ref',
        'reftok',
        'relatedposts_hit',
        'relatedposts_origin',
        'relatedposts_position',
        'remodel',
        'replytocom',
        'rest_route',
        'reverse-paginate',
        'rid',
        'rnd',
        'rndnum',
        'robots_txt',
        'rq',
        'rsd',
        's_kwcid',
        'sa',
        'safe',
        'said',
        'sales_cat',
        'sales_kw',
        'sb_referer_host',
        'scrape',
        'script',
        'scrlybrkr',
        'search',
        'sellid',
        'sersafe',
        'sfn_data',
        'sfn_trk',
        'sfns',
        'sfw',
        'sha1',
        'share',
        'shared',
        'showcomment',
        'showComment',
        'si',
        'sid',
        'sid1',
        'sid2',
        'sidewalkShow',
        'sig',
        'site',
        'site_id',
        'siteid',
        'slicer1',
        'slicer2',
        'source',
        'spref',
        'spvb',
        'sra',
        'src',
        'srk',
        'srp',
        'ssp_iabi',
        'ssts',
        'stylishmurphybeds',
        'subId1 ',
        'subId2 ',
        'subId3',
        'subid',
        'swcfpc',
        'tail',
        'teaser',
        'test',
        'timezone',
        'toWww',
        'triplesource',
        'trk_contact',
        'trk_module',
        'trk_msg',
        'trk_sid',
        'tsig',
        'turl',
        'u',
        'unapproved',
        'up_auto_log',
        'upage',
        'updated-max',
        'uptime',
        'us_privacy',
        'usegapi',
        'userConsent',
        'usqp',
        'utm',
        'utm_campa',
        'utm_campaign',
        'utm_content',
        'utm_expid',
        'utm_id',
        'utm_medium',
        'utm_reader',
        'utm_referrer',
        'utm_source',
        'utm_sq',
        'utm_ter',
        'utm_term',
        'v',
        'vc',
        'vf',
        'vgo_ee',
        'vp',
        'vrw',
        'vz',
        'wbraid',
        'webdriver',
        'wing',
        'wpdParentID',
        'wpmp_switcher',
        'wref',
        'wswy',
        'wtime',
        'x',
        'zMoatImpID',
        'zarsrc',
        'zeffdn'
    ];
}