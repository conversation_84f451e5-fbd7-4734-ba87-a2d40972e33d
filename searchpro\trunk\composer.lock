{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4e7f747dc81cfce08ebcaa931628ca89", "packages": [{"name": "woocommerce/action-scheduler", "version": "3.8.1", "source": {"type": "git", "url": "https://github.com/woocommerce/action-scheduler.git", "reference": "e331b534d7de10402d7545a0de50177b874c0779"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/action-scheduler/zipball/e331b534d7de10402d7545a0de50177b874c0779", "reference": "e331b534d7de10402d7545a0de50177b874c0779", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^7.5", "woocommerce/woocommerce-sniffs": "0.1.0", "wp-cli/wp-cli": "~2.5.0", "yoast/phpunit-polyfills": "^2.0"}, "type": "wordpress-plugin", "extra": {"scripts-description": {"test": "Run unit tests", "phpcs": "Analyze code against the WordPress coding standards with PHP_CodeSniffer", "phpcbf": "Fix coding standards warnings/errors automatically with PHP Code Beautifier"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "Action Scheduler for WordPress and WooCommerce", "homepage": "https://actionscheduler.org/", "support": {"issues": "https://github.com/woocommerce/action-scheduler/issues", "source": "https://github.com/woocommerce/action-scheduler/tree/3.8.1"}, "time": "2024-06-20T19:53:06+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}